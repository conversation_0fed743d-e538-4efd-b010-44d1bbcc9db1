# Purro Extension - Hyperliquid Wallet

> **Purro** is a Chrome extension wallet specifically designed for the Hyperliquid ecosystem and other EVM networks. This wallet provides a user-friendly and secure interface for managing crypto assets and interacting with dApps.

## 🎯 Key Features

### 🔐 Secure Wallet Management
- **Create new wallet** with 12-word seed phrase
- **Import wallet** from seed phrase or private key
- **Watch-only accounts** for address monitoring
- **Data encryption** with password protection
- **Auto-lock** after inactivity period

### 🌐 Multi-Network Support
- **Hyperliquid EVM** (mainnet & testnet) - Primary network
- **Ethereum** mainnet
- **Arbitrum** One
- **Base** mainnet
- **Solana** (in development)
- **Sui** (in development)

### 🔗 dApp Integration
- **EIP-1193** standard support
- **EIP-6963** multi-provider discovery
- **EIP-712** typed data signing
- **Legacy methods** compatibility
- **Provider injection** for web pages

### 🎨 User Interface
- **Modern UI** with Tailwind CSS
- **Responsive design** 
- **Dark theme** by default
- **Smooth animations** with Framer Motion
- **Side panel** support

## 🏗️ Architecture

```
purro-extension/
├── src/
│   ├── background/          # Background scripts
│   │   ├── providers/       # Blockchain providers
│   │   ├── handlers/        # Message handlers
│   │   ├── lib/            # Core libraries
│   │   └── types/          # Type definitions
│   ├── client/             # UI components
│   │   ├── component/      # React components
│   │   ├── screens/        # App screens
│   │   ├── hooks/          # Custom hooks
│   │   └── services/       # API services
│   ├── assets/             # Static assets
│   └── manifest.json       # Extension manifest
├── html/                   # HTML entry points
├── docs/                   # Documentation
└── public/                 # Public assets
```

### Core Components

#### 1. Background Script (`src/background/`)
- **Provider Manager**: Manages blockchain providers
- **EVM Provider**: EIP-1193 implementation for EVM chains
- **Message Handler**: Handles communication between UI and background
- **Account Handler**: Manages accounts and keys
- **Storage Handler**: Encrypted storage management

#### 2. UI Client (`src/client/`)
- **React components** with TypeScript
- **Zustand** for state management
- **TanStack Query** for data fetching
- **Tailwind CSS** for styling

#### 3. Provider System
- **EIP-1193** compliant Ethereum provider
- **EIP-6963** multi-provider discovery
- **Content script injection** into web pages
- **Event system** for dApp communication

## 🚀 Installation and Development

### System Requirements
- Node.js 18+
- pnpm (package manager)
- Chrome browser

### Install dependencies
```bash
pnpm install
```

### Development
```bash
# Build and watch changes
pnpm run build:watch

# Type checking
pnpm run type-check

# Run tests
pnpm run test:handlers
```

### Build production
```bash
pnpm run build
```

### Load extension in Chrome
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select the `dist` folder

## 📱 Usage

### First-time Setup
1. **Install extension** from Chrome Web Store (coming soon)
2. **Create new wallet** or import existing wallet
3. **Create password** to protect wallet
4. **Backup seed phrase** securely

### Account Management
- **Create new account** for different chains
- **Import private key** or seed phrase
- **Switch accounts** easily
- **View balances** across multiple networks

### dApp Interaction
- **Connect wallet** with dApps
- **Sign transactions** securely
- **Manage permissions** per-site
- **Switch networks** as requested by dApp

## 🔧 API Documentation

### EVM Provider
Purro provides complete EVM provider API:

```javascript
// Detect provider
const provider = window.ethereum; // or window.purro

// Connect wallet
const accounts = await provider.request({ 
  method: 'eth_requestAccounts' 
});

// Send transaction
const txHash = await provider.request({
  method: 'eth_sendTransaction',
  params: [{
    from: accounts[0],
    to: '0x...',
    value: '0x...'
  }]
});

// Sign message
const signature = await provider.request({
  method: 'personal_sign',
  params: ['Hello World!', accounts[0]]
});
```

### Supported RPC Methods
- `eth_requestAccounts` - Request account access
- `eth_accounts` - Get connected accounts
- `eth_chainId` - Get current chain ID
- `eth_sendTransaction` - Send transaction
- `personal_sign` - Sign message
- `eth_signTypedData_v4` - Sign typed data
- `wallet_switchEthereumChain` - Switch chain
- `wallet_addEthereumChain` - Add new chain

## 🧪 Testing

### Unit Tests
```bash
# Test storage handler
pnpm run test:storage

# Test account handler
pnpm run test:account

# Test encryption
pnpm run test:encryption

# Run all tests
pnpm run test:handlers
```

### Provider Testing
Open `test-dapp-detection.html` in browser to test provider functionality.

## 🔒 Security

### Encryption
- **AES-256-GCM** encryption for sensitive data
- **PBKDF2** key derivation from password
- **Secure random** seed generation

### Permission System
- **Origin validation** for all requests
- **User confirmation** for transactions
- **Per-site permissions** management
- **Session timeout** protection

### Best Practices
- **Never store** private keys in plaintext
- **Always verify** transaction details
- **Use hardware wallets** when possible
- **Regular backups** of seed phrases

## 🌟 Roadmap

### Phase 1 (Current)
- ✅ Basic wallet functionality
- ✅ Hyperliquid EVM support
- ✅ Multi-chain support
- ✅ dApp integration

### Phase 2 (Coming Soon)
- 🔄 Solana integration
- 🔄 Sui integration
- 🔄 NFT support
- 🔄 DeFi integrations

### Phase 3 (Future)
- 📋 Hardware wallet support
- 📋 Mobile app
- 📋 Advanced trading features
- 📋 Portfolio analytics

## 🤝 Contributing

### How to Contribute
1. Fork repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Create Pull Request

### Coding Standards
- **TypeScript** for type safety
- **ESLint** for code quality
- **Prettier** for code formatting
- **Conventional commits** for commit messages

### Bug Reports
- Use GitHub Issues
- Provide steps to reproduce
- Include browser and extension version
- Attach console logs if available

## 📞 Support

### Community
- **Discord**: [https://discord.gg/VJunuK9T5w](https://discord.gg/VJunuK9T5w)
- **Twitter**: [@purro_xyz](https://x.com/purro_xyz)
- **Telegram**: [https://t.me/purro_xyz](https://t.me/purro_xyz)

### Documentation
- **API Docs**: [docs/EVM_PROVIDER_README.md](docs/EVM_PROVIDER_README.md)
- **Architecture**: [docs/evm-provider-architecture.md](docs/evm-provider-architecture.md)
- **Communication**: [docs/evm-provider-communication.md](docs/evm-provider-communication.md)

### Website
- **Homepage**: [https://purro.xyz](https://purro.xyz)
- **Help Center**: [https://purro.xyz/help](https://purro.xyz/help)

## 📄 License

MIT License - see [LICENSE](LICENSE) file for more details.

## 🐱 About Purro

**Purro** was created with the goal of making Web3 accessible and secure for everyone. We believe that DeFi and blockchain technology will change the future of finance, and Purro is the bridge that helps users easily participate in this new world.

---

💜 **Made with love by the Purro Team** 🐱
