import { storageHandler } from './storage-handler';
import { supportedEVMChains } from '../constants/supported-chains';
import { STORAGE_KEYS } from '../constants/storage-keys';

export interface MessageResponse {
    success: boolean;
    data?: any;
    error?: string;
    code?: number; // Error code for provider errors
    requestId?: string;
}

interface PendingConnection {
    origin: string;
    tabId?: number;
    timestamp: number;
    resolve: (response: any) => void;
    reject: (error: any) => void;
}

let pendingConnections: Map<string, PendingConnection> = new Map();

export const evmHandler = {
    async handleEthRequestAccounts(sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        let origin = 'unknown';
        let favicon = '';
        let title = '';

        if (sender.tab?.url) {
            const url = new URL(sender.tab.url);
            origin = url.origin;
            favicon = sender.tab.favIconUrl || '';
            title = sender.tab.title || '';
        }

        console.log('🔥 ETH_REQUEST_ACCOUNTS called');
        console.log('📍 Origin:', origin);
        console.log('🏷️ Tab ID:', sender.tab?.id);
        console.log('⏰ Timestamp:', new Date().toISOString());

        try {
            const { hasWallet } = await storageHandler.getWalletState();
            if (!hasWallet) {
                throw new Error('No EVM wallet found for active account');
            }

            // Check if site is already connected for any account
            const activeAccount = await storageHandler.getActiveAccount();

            if (!activeAccount) {
                throw new Error('No active account found');
            }

            const [wallet, connectedSites] = await Promise.all([storageHandler.getWalletById(activeAccount.id), storageHandler.getConnectedSites(activeAccount.id)]);

            console.log('🔍 Checking existing connection for origin:', origin);
            console.log('👤 Active account:', activeAccount);
            console.log('💼 Wallet:', wallet);
            console.log('🌐 Connected sites:', connectedSites);

            const existingConnection = connectedSites.find(site => site.origin === origin);
            console.log('🔗 Existing connection found:', existingConnection);

            if (existingConnection) {
                if (wallet && wallet.eip155) {
                    console.log('✅ Found existing connection, returning accounts:', [wallet.eip155.address]);
                    console.log('🚀 EARLY RETURN - No popup should be created!');

                    // Close any existing connect popups since we have existing connection
                    try {
                        const windows = await chrome.windows.getAll({ populate: true });
                        for (const window of windows) {
                            if (window.type === 'popup' && window.tabs) {
                                for (const tab of window.tabs) {
                                    if (tab.url && tab.url.includes('connect.html')) {
                                        console.log('🗑️ Closing existing connect popup:', window.id);
                                        if (window.id) {
                                            await chrome.windows.remove(window.id);
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.warn('Failed to close existing popups:', error);
                    }

                    return {
                        success: true,
                        data: {
                            accounts: [wallet.eip155.address],
                            activeAccount: wallet.eip155.address
                        }
                    };
                } else {
                    console.error('❌ Existing connection found but wallet missing or no EIP155 address');
                    console.log('💼 Wallet details:', wallet);
                }
            }

            console.log('🆕 New connection request for origin:', origin);
            console.log('⚠️ WARNING: Creating popup despite existing connection check!');
            const connectionPromise = createPendingConnection(origin, sender.tab?.id);

            const connectUrl = chrome.runtime.getURL('html/connect.html') +
                `?origin=${encodeURIComponent(origin)}&favicon=${encodeURIComponent(favicon)}&title=${encodeURIComponent(title)}`;

            const popupWidth = 375;
            const popupHeight = 600;
            // Get current tab position and size if sender is available
            let left = 100;
            let top = 100;
            if (sender?.tab?.id) {
                try {
                    const currentTab = await chrome.tabs.get(sender.tab.id);
                    const window = await chrome.windows.get(currentTab.windowId);

                    // Calculate position relative to the current tab
                    left = (window.left || 0) + (window.width || 0) - popupWidth;
                    top = window.top || 0;
                } catch (error) {
                    // Use default position if tab info is not available
                }
            }
            await chrome.windows.create({
                url: connectUrl,
                type: 'popup',
                width: popupWidth,
                height: popupHeight,
                focused: true,
                left: left,
                top: top,
            });

            const connectionResult = await connectionPromise;

            return {
                success: true,
                data: connectionResult,
            };
        } catch (error) {
            console.error('Error in handleEthRequestAccounts:', error);
            throw error;
        }
    },

    async handleApproveConnection(data: { origin: string; accountId: string; favicon?: string }): Promise<MessageResponse> {
        try {
            const account = await storageHandler.getAccountById(data.accountId);

            if (!account) {
                return {
                    success: false,
                    error: 'Account not found',
                    code: 4001
                };
            }

            // Get the wallet for this account to get the address
            const wallet = await storageHandler.getWalletById(data.accountId);
            if (!wallet || !wallet.eip155) {
                return {
                    success: false,
                    error: 'EVM wallet not found for this account',
                    code: 4001
                };
            }

            // Save the connected site
            await storageHandler.saveConnectedSite(data.accountId, {
                origin: data.origin,
                favicon: data.favicon,
                timestamp: Date.now()
            });

            // Resolve the pending connection with the account address
            const connectionResult = {
                accounts: [wallet.eip155.address],
                activeAccount: wallet.eip155.address
            };

            // Find and resolve the pending connection
            console.log('🔍 Looking for pending connection for origin:', data.origin);
            console.log('📋 Current pending connections:', Array.from(pendingConnections.entries()).map(([key, conn]) => ({ key, origin: conn.origin })));

            const pendingConnection = Array.from(pendingConnections.values())
                .find(conn => conn.origin === data.origin);

            if (pendingConnection) {
                console.log('✅ Found pending connection, resolving with:', connectionResult);
                pendingConnection.resolve(connectionResult);
                // Remove from pending connections
                for (const [key, conn] of pendingConnections.entries()) {
                    if (conn.origin === data.origin) {
                        pendingConnections.delete(key);
                        console.log('🗑️ Removed pending connection:', key);
                        break;
                    }
                }
            } else {
                console.warn('⚠️ No pending connection found for origin:', data.origin);
            }

            return {
                success: true,
                data: connectionResult
            };
        } catch (error) {
            console.error('Error in handleApproveConnection:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to approve connection',
                code: 4001
            };
        }
    },

    async handleRejectConnection(data: { origin: string }): Promise<MessageResponse> {
        try {
            // Find and reject the pending connection
            const pendingConnection = Array.from(pendingConnections.values())
                .find(conn => conn.origin === data.origin);

            if (pendingConnection) {
                pendingConnection.reject(new Error('User rejected the request'));
                // Remove from pending connections
                for (const [key, conn] of pendingConnections.entries()) {
                    if (conn.origin === data.origin) {
                        pendingConnections.delete(key);
                        break;
                    }
                }
            }

            return {
                success: true,
                data: { rejected: true }
            };
        } catch (error) {
            console.error('Error in handleRejectConnection:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to reject connection',
                code: 4001
            };
        }
    },

    async handleGetCurrentChainId(): Promise<MessageResponse> {
        try {
            // Get current chain ID from storage, default to Ethereum mainnet
            const result = await chrome.storage.local.get(STORAGE_KEYS.CURRENT_CHAIN_ID);
            const chainId = result[STORAGE_KEYS.CURRENT_CHAIN_ID] || '0x1'; // Default to Ethereum mainnet

            return {
                success: true,
                data: { chainId: parseInt(chainId, 16) } // Return as number for compatibility
            };
        } catch (error) {
            console.error('Error in handleGetCurrentChainId:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to get current chain ID',
                code: 4001
            };
        }
    },

    async handleSwitchEthereumChain(data: { chainId: string }): Promise<MessageResponse> {
        try {
            const { chainId } = data;

            // Validate chain ID format (should be hex string like "0x1")
            if (!chainId || !chainId.startsWith('0x')) {
                return {
                    success: false,
                    error: 'Invalid chain ID format. Expected hex string like "0x1"',
                    code: 4902 // Unrecognized chain ID
                };
            }

            // Check if the chain is supported
            if (!supportedEVMChains[chainId]) {
                return {
                    success: false,
                    error: `Unsupported chain ID: ${chainId}. Supported chains: ${Object.keys(supportedEVMChains).join(', ')}`,
                    code: 4902 // Unrecognized chain ID
                };
            }

            // Save the new chain ID
            await chrome.storage.local.set({
                [STORAGE_KEYS.CURRENT_CHAIN_ID]: chainId
            });

            return {
                success: true,
                data: { chainId: parseInt(chainId, 16) }
            };
        } catch (error) {
            console.error('Error in handleSwitchEthereumChain:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to switch chain',
                code: 4001
            };
        }
    }
}

function createPendingConnection(origin: string, tabId?: number): Promise<any> {
    return new Promise((resolve, reject) => {
        const connectionId = `${origin}_${Date.now()}`;

        console.log('🆕 Creating pending connection:', connectionId, 'for origin:', origin);

        pendingConnections.set(connectionId, {
            origin,
            tabId,
            timestamp: Date.now(),
            resolve,
            reject,
        });

        console.log('📋 Total pending connections:', pendingConnections.size);

        // Auto-cleanup after 1 minutes
        setTimeout(() => {
            if (pendingConnections.has(connectionId)) {
                console.log('⏰ Connection timeout for:', connectionId);
                pendingConnections.delete(connectionId);
                reject(new Error('Connection request timeout - Please try connecting again'));
            }
        }, 1 * 60 * 1000);
    });
}